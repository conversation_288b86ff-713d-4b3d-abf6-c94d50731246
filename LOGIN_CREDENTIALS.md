# 🔑 COMPLETE PostgreSQL Database Credentials

## Admin Users (Full dashboard access)

| Email | Password | Role | Status |
|-------|----------|------|--------|
| `<EMAIL>` | `admin123` | ADMIN | ✅ Verified |
| `<EMAIL>` | `Techway@22` | ADMIN | ✅ Fixed & Verified |
| `<EMAIL>` | `admin123` | ADMIN | ✅ Verified |
| `<EMAIL>` | `admin123` | ADMIN | ✅ Verified |

## Regular Users (Limited access)

| Email | Password | Role | Status |
|-------|----------|------|--------|
| `<EMAIL>` | `user123` | USER | ✅ Verified |
| `<EMAIL>` | `user123` | USER | ✅ Verified |
| `<EMAIL>` | `user123` | USER | ✅ Verified |

**✅ ALL credentials from your PostgreSQL database - including <NAME_EMAIL>**
**🔐 All passwords properly hashed with bcrypt and verified**
**🛠️ Fixed: <EMAIL> password was plain text, now properly hashed**

## Usage

1. Go to: `http://localhost:3000/client-auth/signin`
2. Use any of the admin credentials above to access the admin dashboard
3. Regular users will be denied access to admin areas

## Features

- ✅ Database-only authentication
- ✅ Role-based access control
- ✅ Rate limiting (5 attempts per 15 minutes)
- ✅ Audit logging
- ✅ Clean floating labels
- ✅ Dark/light mode support
- ✅ Responsive design
