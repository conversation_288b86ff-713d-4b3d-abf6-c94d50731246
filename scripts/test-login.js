const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function testLogin() {
  try {
    console.log('🔍 Testing login credentials...')
    
    const email = '<EMAIL>'
    const password = 'admin123'
    
    // Find user
    const user = await prisma.users.findUnique({
      where: { email: email.toLowerCase().trim() }
    })
    
    if (!user) {
      console.log('❌ User not found!')
      return
    }
    
    console.log('✅ User found:')
    console.log(`   ID: ${user.id}`)
    console.log(`   Email: ${user.email}`)
    console.log(`   Role: ${user.role}`)
    console.log(`   Active: ${user.isactive}`)
    console.log(`   Has Password: ${!!user.password}`)
    
    // Test password
    if (!user.password) {
      console.log('❌ No password set for user!')
      return
    }
    
    if (!user.isactive) {
      console.log('❌ User account is inactive!')
      return
    }
    
    const isPasswordValid = await bcrypt.compare(password, user.password)
    console.log(`\n🔐 Password validation for "${password}": ${isPasswordValid ? '✅ Valid' : '❌ Invalid'}`)
    
    if (isPasswordValid) {
      console.log('\n🎉 Login should work with these credentials:')
      console.log(`   Email: ${email}`)
      console.log(`   Password: ${password}`)
      console.log(`   URL: http://localhost:3001/client-auth/signin`)
    } else {
      console.log('\n🔧 Password mismatch - resetting password...')
      const newPasswordHash = await bcrypt.hash(password, 12)
      
      await prisma.users.update({
        where: { email: email },
        data: { password: newPasswordHash }
      })
      
      console.log('✅ Password reset successfully!')
      console.log(`   Email: ${email}`)
      console.log(`   Password: ${password}`)
    }
    
  } catch (error) {
    console.error('❌ Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testLogin()
